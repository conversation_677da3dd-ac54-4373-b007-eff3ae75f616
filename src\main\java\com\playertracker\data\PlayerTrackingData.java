package com.playertracker.data;

import com.playertracker.PlayerTrackerMod;
import net.minecraft.registry.RegistryKey;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.network.ServerPlayerEntity;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.GlobalPos;
import net.minecraft.world.World;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

public class PlayerTrackingData {
    private static MinecraftServer server;

    // Player name -> Current position
    private static final Map<String, GlobalPos> playerPositions = new ConcurrentHashMap<>();

    // Player name -> Dimension -> Last position in that dimension
    private static final Map<String, Map<RegistryKey<World>, GlobalPos>> playerDimensionHistory = new ConcurrentHashMap<>();

    public static void setServer(MinecraftServer minecraftServer) {
        server = minecraftServer;
    }

    public static void updatePlayerPosition(ServerPlayerEntity player) {
        String playerName = player.getGameProfile().getName();
        RegistryKey<World> dimension = player.getWorld().getRegistryKey();
        BlockPos pos = player.getBlockPos();

        GlobalPos globalPos = GlobalPos.create(dimension, pos);

        // Update current position
        playerPositions.put(playerName, globalPos);

        // Update dimension history
        playerDimensionHistory.computeIfAbsent(playerName, k -> new ConcurrentHashMap<>())
            .put(dimension, globalPos);

        PlayerTrackerMod.LOGGER.debug("Updated position for player {}: {} in {}",
            playerName, pos, dimension.getValue());
    }

    public static Optional<GlobalPos> getPlayerPosition(String playerName) {
        return Optional.ofNullable(playerPositions.get(playerName));
    }

    public static Optional<GlobalPos> getLastPositionInDimension(String playerName, RegistryKey<World> dimension) {
        Map<RegistryKey<World>, GlobalPos> dimensionHistory = playerDimensionHistory.get(playerName);
        if (dimensionHistory == null) return Optional.empty();

        return Optional.ofNullable(dimensionHistory.get(dimension));
    }

    public static Set<String> getTrackedPlayers() {
        return new HashSet<>(playerPositions.keySet());
    }

    public static boolean isPlayerOnline(String playerName) {
        if (server == null) return false;

        return server.getPlayerManager().getPlayer(playerName) != null;
    }
}
