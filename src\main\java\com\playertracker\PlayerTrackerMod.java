package com.playertracker;

import com.playertracker.command.TrackCommand;
import com.playertracker.data.PlayerTrackingData;
import com.playertracker.item.ModItems;
import net.fabricmc.api.ModInitializer;
import net.fabricmc.fabric.api.command.v2.CommandRegistrationCallback;
import net.fabricmc.fabric.api.event.lifecycle.v1.ServerLifecycleEvents;
import net.fabricmc.fabric.api.networking.v1.ServerPlayConnectionEvents;
import net.minecraft.server.MinecraftServer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class PlayerTrackerMod implements ModInitializer {
    public static final String MOD_ID = "player-tracker-compass";
    public static final Logger LOGGER = LoggerFactory.getLogger(MOD_ID);

    private static ScheduledExecutorService scheduler;

    @Override
    public void onInitialize() {
        LOGGER.info("Initializing Player Tracker Compass Mod");
        
        // Initialize items
        ModItems.initialize();
        
        // Register commands
        CommandRegistrationCallback.EVENT.register(TrackCommand::register);
        
        // Register server events
        ServerLifecycleEvents.SERVER_STARTED.register(server -> {
            PlayerTrackingData.setServer(server);
            startPlayerPositionUpdater(server);
        });

        ServerPlayConnectionEvents.JOIN.register((handler, sender, server) -> {
            PlayerTrackingData.updatePlayerPosition(handler.getPlayer());
        });

        ServerPlayConnectionEvents.DISCONNECT.register((handler, server) -> {
            PlayerTrackingData.updatePlayerPosition(handler.getPlayer());
        });
        
        LOGGER.info("Player Tracker Compass Mod initialized successfully");
    }

    private static void startPlayerPositionUpdater(MinecraftServer server) {
        if (scheduler != null) {
            scheduler.shutdown();
        }

        scheduler = Executors.newSingleThreadScheduledExecutor();

        // Update player positions every 5 seconds
        scheduler.scheduleAtFixedRate(() -> {
            try {
                server.getPlayerManager().getPlayerList().forEach(PlayerTrackingData::updatePlayerPosition);
            } catch (Exception e) {
                LOGGER.error("Error updating player positions", e);
            }
        }, 5, 5, TimeUnit.SECONDS);

        LOGGER.info("Started player position updater");
    }
}
