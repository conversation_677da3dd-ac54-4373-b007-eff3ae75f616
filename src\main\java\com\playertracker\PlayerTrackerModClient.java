package com.playertracker;

import net.fabricmc.api.ClientModInitializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class PlayerTrackerModClient implements ClientModInitializer {
    public static final Logger LOGGER = LoggerFactory.getLogger(PlayerTrackerMod.MOD_ID);

    @Override
    public void onInitializeClient() {
        LOGGER.info("Initializing Player Tracker Compass Mod Client");
        
        // Client-side initialization if needed
        
        LOGGER.info("Player Tracker Compass Mod Client initialized successfully");
    }
}
