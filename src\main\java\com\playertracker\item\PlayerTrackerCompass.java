package com.playertracker.item;

import com.playertracker.data.PlayerTrackingData;
import net.minecraft.component.DataComponentTypes;
import net.minecraft.component.type.LodestoneTrackerComponent;
import net.minecraft.component.type.NbtComponent;
import net.minecraft.entity.Entity;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.item.Item;
import net.minecraft.item.ItemStack;
import net.minecraft.item.tooltip.TooltipType;
import net.minecraft.nbt.NbtCompound;
import net.minecraft.server.network.ServerPlayerEntity;
import net.minecraft.text.Text;
import net.minecraft.util.Formatting;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.GlobalPos;
import net.minecraft.world.World;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

public class PlayerTrackerCompass extends Item {
    
    public PlayerTrackerCompass(Settings settings) {
        super(settings);
    }
    
    public void inventoryTick(ItemStack stack, World world, Entity entity, int slot, boolean selected) {
        if (world.isClient || !(entity instanceof ServerPlayerEntity player)) {
            return;
        }

        // Update compass direction based on tracked player
        updateCompassDirection(stack, player);
    }

    private void updateCompassDirection(ItemStack stack, ServerPlayerEntity holder) {
        String targetPlayerName = getTargetPlayerName(stack);
        if (targetPlayerName == null || targetPlayerName.isEmpty()) {
            return;
        }

        Optional<GlobalPos> targetPos = PlayerTrackingData.getPlayerPosition(targetPlayerName);
        if (targetPos.isPresent()) {
            GlobalPos pos = targetPos.get();

            // If target is in same dimension, point to their current position
            if (pos.dimension().equals(holder.getWorld().getRegistryKey())) {
                LodestoneTrackerComponent tracker = new LodestoneTrackerComponent(
                    Optional.of(pos), true
                );
                stack.set(DataComponentTypes.LODESTONE_TRACKER, tracker);
            } else {
                // If target is in different dimension, point to their last known position in current dimension
                Optional<GlobalPos> lastPosInDimension = PlayerTrackingData.getLastPositionInDimension(
                    targetPlayerName, holder.getWorld().getRegistryKey()
                );

                if (lastPosInDimension.isPresent()) {
                    LodestoneTrackerComponent tracker = new LodestoneTrackerComponent(
                        Optional.of(lastPosInDimension.get()), true
                    );
                    stack.set(DataComponentTypes.LODESTONE_TRACKER, tracker);
                }
            }
        }
    }
    
    // 简化的存储方式，使用静态Map
    private static final Map<ItemStack, String> trackedPlayers = new HashMap<>();

    public static void setTargetPlayer(ItemStack stack, String playerName) {
        trackedPlayers.put(stack, playerName);
    }

    public static String getTargetPlayerName(ItemStack stack) {
        return trackedPlayers.getOrDefault(stack, "");
    }
    
    public void appendTooltip(ItemStack stack, TooltipContext context, List<Text> tooltip, TooltipType type) {

        String targetPlayer = getTargetPlayerName(stack);
        if (targetPlayer != null && !targetPlayer.isEmpty()) {
            tooltip.add(Text.literal("Tracking: " + targetPlayer).formatted(Formatting.GOLD));

            Optional<GlobalPos> targetPos = PlayerTrackingData.getPlayerPosition(targetPlayer);
            if (targetPos.isPresent()) {
                GlobalPos pos = targetPos.get();
                tooltip.add(Text.literal("Position: " + pos.pos().getX() + ", " + pos.pos().getY() + ", " + pos.pos().getZ())
                    .formatted(Formatting.GRAY));
                tooltip.add(Text.literal("Dimension: " + pos.dimension().getValue().toString())
                    .formatted(Formatting.GRAY));
            } else {
                tooltip.add(Text.literal("Player not found").formatted(Formatting.RED));
            }
        } else {
            tooltip.add(Text.literal("No target set").formatted(Formatting.GRAY));
            tooltip.add(Text.literal("Use /track <player> to set target").formatted(Formatting.YELLOW));
        }
    }
}
