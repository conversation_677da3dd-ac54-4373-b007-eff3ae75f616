package com.playertracker.item;

import com.playertracker.PlayerTrackerMod;
import net.minecraft.item.Item;
import net.minecraft.registry.Registries;
import net.minecraft.registry.Registry;
import net.minecraft.util.Identifier;

public class ModItems {
    
    public static final PlayerTrackerCompass PLAYER_TRACKER_COMPASS = new PlayerTrackerCompass(
        new Item.Settings().maxCount(1)
    );
    
    public static void initialize() {
        Registry.register(
            Registries.ITEM,
            Identifier.of(PlayerTrackerMod.MOD_ID, "player_tracker_compass"),
            PLAYER_TRACKER_COMPASS
        );
        
        PlayerTrackerMod.LOGGER.info("Registered mod items");
    }
}
