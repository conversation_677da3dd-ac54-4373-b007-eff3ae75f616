package com.playertracker.command;

import com.mojang.brigadier.CommandDispatcher;
import com.mojang.brigadier.arguments.StringArgumentType;
import com.mojang.brigadier.context.CommandContext;
import com.mojang.brigadier.exceptions.CommandSyntaxException;
import com.mojang.brigadier.suggestion.SuggestionProvider;
import com.playertracker.data.PlayerTrackingData;
import com.playertracker.item.ModItems;
import com.playertracker.item.PlayerTrackerCompass;
import net.minecraft.command.CommandSource;
import net.minecraft.command.argument.EntityArgumentType;
import net.minecraft.item.ItemStack;
import net.minecraft.server.command.CommandManager;
import net.minecraft.server.command.ServerCommandSource;
import net.minecraft.server.network.ServerPlayerEntity;
import net.minecraft.text.Text;
import net.minecraft.util.Formatting;

import java.util.Arrays;
import java.util.Collection;

public class TrackCommand {
    
    private static final SuggestionProvider<ServerCommandSource> PLAYER_SUGGESTIONS =
        (context, builder) -> {
            Collection<String> playerNames = Arrays.asList(context.getSource().getServer().getPlayerNames());
            return CommandSource.suggestMatching(playerNames, builder);
        };
    
    public static void register(CommandDispatcher<ServerCommandSource> dispatcher,
                               net.minecraft.command.CommandRegistryAccess registryAccess,
                               CommandManager.RegistrationEnvironment registrationEnvironment) {
        dispatcher.register(
            CommandManager.literal("track")
                .requires(source -> source.hasPermissionLevel(0)) // Allow all players
                .then(CommandManager.argument("target", StringArgumentType.word())
                    .suggests(PLAYER_SUGGESTIONS)
                    .executes(TrackCommand::executeTrack))
                .executes(TrackCommand::executeGetCompass)
        );
        
        dispatcher.register(
            CommandManager.literal("untrack")
                .requires(source -> source.hasPermissionLevel(0))
                .executes(TrackCommand::executeUntrack)
        );
        
        dispatcher.register(
            CommandManager.literal("trackinfo")
                .requires(source -> source.hasPermissionLevel(0))
                .executes(TrackCommand::executeTrackInfo)
        );
    }
    
    private static int executeTrack(CommandContext<ServerCommandSource> context) throws CommandSyntaxException {
        ServerCommandSource source = context.getSource();
        ServerPlayerEntity player = source.getPlayerOrThrow();
        String targetPlayerName = StringArgumentType.getString(context, "target");
        
        // Check if target player exists or has been tracked before
        boolean targetExists = source.getServer().getPlayerManager().getPlayer(targetPlayerName) != null ||
                              PlayerTrackingData.getTrackedPlayers().contains(targetPlayerName);
        
        if (!targetExists) {
            source.sendFeedback(() -> Text.literal("Player '" + targetPlayerName + "' not found or never been online.")
                .formatted(Formatting.RED), false);
            return 0;
        }
        
        // Check if player is trying to track themselves
        if (targetPlayerName.equals(player.getGameProfile().getName())) {
            source.sendFeedback(() -> Text.literal("You cannot track yourself!")
                .formatted(Formatting.RED), false);
            return 0;
        }
        
        // Give player a tracking compass or update existing one
        ItemStack compass = findOrCreateTrackingCompass(player);
        PlayerTrackerCompass.setTargetPlayer(compass, targetPlayerName);
        
        // Update player position data
        ServerPlayerEntity targetPlayer = source.getServer().getPlayerManager().getPlayer(targetPlayerName);
        if (targetPlayer != null) {
            PlayerTrackingData.updatePlayerPosition(targetPlayer);
        }
        
        source.sendFeedback(() -> Text.literal("Now tracking player: " + targetPlayerName)
            .formatted(Formatting.GREEN), false);
        
        if (!PlayerTrackingData.isPlayerOnline(targetPlayerName)) {
            source.sendFeedback(() -> Text.literal("Note: " + targetPlayerName + " is currently offline. " +
                "Compass will point to their last known position.")
                .formatted(Formatting.YELLOW), false);
        }
        
        return 1;
    }
    
    private static int executeGetCompass(CommandContext<ServerCommandSource> context) throws CommandSyntaxException {
        ServerCommandSource source = context.getSource();
        ServerPlayerEntity player = source.getPlayerOrThrow();
        
        // Give player a new tracking compass
        ItemStack compass = new ItemStack(ModItems.PLAYER_TRACKER_COMPASS);
        
        if (player.getInventory().insertStack(compass)) {
            source.sendFeedback(() -> Text.literal("Given you a Player Tracker Compass! " +
                "Use '/track <player>' to set a target.")
                .formatted(Formatting.GREEN), false);
        } else {
            source.sendFeedback(() -> Text.literal("Your inventory is full!")
                .formatted(Formatting.RED), false);
        }
        
        return 1;
    }
    
    private static int executeUntrack(CommandContext<ServerCommandSource> context) throws CommandSyntaxException {
        ServerCommandSource source = context.getSource();
        ServerPlayerEntity player = source.getPlayerOrThrow();
        
        // Find tracking compass in inventory and clear target
        boolean found = false;
        for (int i = 0; i < player.getInventory().size(); i++) {
            ItemStack stack = player.getInventory().getStack(i);
            if (stack.getItem() instanceof PlayerTrackerCompass) {
                PlayerTrackerCompass.setTargetPlayer(stack, "");
                found = true;
            }
        }
        
        if (found) {
            source.sendFeedback(() -> Text.literal("Stopped tracking. Your compass is now reset.")
                .formatted(Formatting.YELLOW), false);
        } else {
            source.sendFeedback(() -> Text.literal("You don't have a Player Tracker Compass!")
                .formatted(Formatting.RED), false);
        }
        
        return found ? 1 : 0;
    }
    
    private static int executeTrackInfo(CommandContext<ServerCommandSource> context) throws CommandSyntaxException {
        ServerCommandSource source = context.getSource();
        ServerPlayerEntity player = source.getPlayerOrThrow();
        
        // Find tracking compass and show info
        for (int i = 0; i < player.getInventory().size(); i++) {
            ItemStack stack = player.getInventory().getStack(i);
            if (stack.getItem() instanceof PlayerTrackerCompass) {
                String targetPlayer = PlayerTrackerCompass.getTargetPlayerName(stack);
                
                if (targetPlayer == null || targetPlayer.isEmpty()) {
                    source.sendFeedback(() -> Text.literal("Your compass is not tracking anyone.")
                        .formatted(Formatting.YELLOW), false);
                } else {
                    boolean isOnline = PlayerTrackingData.isPlayerOnline(targetPlayer);
                    source.sendFeedback(() -> Text.literal("Tracking: " + targetPlayer + 
                        (isOnline ? " (Online)" : " (Offline)"))
                        .formatted(isOnline ? Formatting.GREEN : Formatting.YELLOW), false);
                    
                    PlayerTrackingData.getPlayerPosition(targetPlayer).ifPresent(pos -> {
                        source.sendFeedback(() -> Text.literal("Last known position: " + 
                            pos.pos().getX() + ", " + pos.pos().getY() + ", " + pos.pos().getZ() + 
                            " in " + pos.dimension().getValue())
                            .formatted(Formatting.GRAY), false);
                    });
                }
                return 1;
            }
        }
        
        source.sendFeedback(() -> Text.literal("You don't have a Player Tracker Compass!")
            .formatted(Formatting.RED), false);
        return 0;
    }
    
    private static ItemStack findOrCreateTrackingCompass(ServerPlayerEntity player) {
        // First, try to find existing tracking compass
        for (int i = 0; i < player.getInventory().size(); i++) {
            ItemStack stack = player.getInventory().getStack(i);
            if (stack.getItem() instanceof PlayerTrackerCompass) {
                return stack;
            }
        }
        
        // If no existing compass found, create a new one
        ItemStack compass = new ItemStack(ModItems.PLAYER_TRACKER_COMPASS);
        if (!player.getInventory().insertStack(compass)) {
            // If inventory is full, drop it
            player.dropItem(compass, false);
        }
        
        return compass;
    }
}
