# 玩家追踪指南针 Mod 使用说明

## 🎯 功能概述
这个mod为Minecraft 1.21.6 Fabric服务器提供了玩家追踪功能，允许玩家使用特殊的指南针来追踪其他玩家的位置。

## 📦 安装
1. 确保服务器安装了 Fabric Loader 0.16.14+
2. 确保安装了 Fabric API 0.128.2+1.21.6
3. 将 `player-tracker-compass-1.0.0.jar` 放入服务器的 `mods` 文件夹
4. 重启服务器

## 🎮 指令使用

### 基础指令
- `/track` - 获取一个新的玩家追踪指南针
- `/track <玩家名>` - 设置指南针追踪指定玩家
- `/untrack` - 停止追踪，重置指南针
- `/trackinfo` - 查看当前追踪的玩家信息

### 使用示例
```
/track                    # 获取追踪指南针
/track Steve             # 开始追踪玩家Steve
/trackinfo               # 查看追踪信息：Tracking: Steve (Online/Offline)
/untrack                 # 停止追踪
```

## 🔧 工作原理

1. **获取指南针**：使用 `/track` 命令获取特殊的追踪指南针
2. **设置目标**：使用 `/track <玩家名>` 设置要追踪的玩家
3. **自动追踪**：指南针会自动指向目标玩家的位置
4. **跨维度支持**：如果目标玩家在不同维度，指南针会指向他们在当前维度的最后已知位置
5. **实时更新**：每5秒自动更新一次玩家位置

## 📋 特性

- ✅ 实时玩家位置追踪
- ✅ 跨维度位置记录
- ✅ 服务器重启数据保持
- ✅ 简单易用的指令系统
- ✅ 详细的工具提示信息
- ✅ 无需客户端安装
- ✅ 避免使用Mixin，兼容性好

## 🛠️ 技术信息

- **版本**: 1.0.0
- **Minecraft版本**: 1.21.6
- **Fabric Loader**: 0.16.14+
- **Fabric API**: 0.128.2+1.21.6
- **环境**: 服务器端 (客户端可选)

## 🐛 故障排除

### 常见问题

1. **指南针不指向玩家**
   - 确保目标玩家在线或之前在线过
   - 检查是否正确设置了追踪目标

2. **指令不工作**
   - 确保mod正确安装在服务器上
   - 检查服务器日志是否有错误信息

3. **服务器启动失败**
   - 确保Fabric Loader和Fabric API版本正确
   - 检查是否有其他mod冲突

### 日志信息
mod启动时会在服务器日志中显示：
```
[INFO] Initializing Player Tracker Compass Mod
[INFO] Registered mod items
[INFO] Started player position updater
[INFO] Player Tracker Compass Mod initialized successfully
```

## 📝 更新日志

### v1.0.0
- 初始版本发布
- 基础玩家追踪功能
- 跨维度位置记录
- 指令系统实现

## 🤝 支持

如果遇到问题或有建议，请检查：
1. 服务器日志中的错误信息
2. 确保所有依赖项版本正确
3. 验证mod文件完整性

---

**注意**: 这个mod主要为服务器环境设计，虽然客户端也可以安装，但主要功能在服务器端运行。
