{"schemaVersion": 1, "id": "modid", "version": "${version}", "name": "Example mod", "description": "This is an example description! Tell everyone what your mod is about!", "authors": ["Me!"], "contact": {"homepage": "https://fabricmc.net/", "sources": "https://github.com/FabricMC/fabric-example-mod"}, "license": "CC0-1.0", "icon": "assets/modid/icon.png", "environment": "*", "entrypoints": {"main": ["com.example.ExampleMod"], "client": ["com.example.ExampleModClient"]}, "mixins": ["modid.mixins.json", {"config": "modid.client.mixins.json", "environment": "client"}], "depends": {"fabricloader": ">=0.16.14", "minecraft": "~1.21.8", "java": ">=21", "fabric-api": "*"}, "suggests": {"another-mod": "*"}}