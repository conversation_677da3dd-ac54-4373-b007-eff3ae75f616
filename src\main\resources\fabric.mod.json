{"schemaVersion": 1, "id": "player-tracker-compass", "version": "${version}", "name": "Player Tracker <PERSON>", "description": "A mod that allows players to track other players using a special compass that points to their location.", "authors": ["PlayerTracker"], "contact": {"homepage": "https://fabricmc.net/", "sources": "https://github.com/FabricMC/fabric-example-mod"}, "license": "CC0-1.0", "icon": "assets/player-tracker-compass/icon.png", "environment": "*", "entrypoints": {"main": ["com.playertracker.PlayerTrackerMod"], "client": ["com.playertracker.PlayerTrackerModClient"]}, "mixins": ["player-tracker-compass.mixins.json"], "depends": {"fabricloader": ">=0.16.14", "minecraft": "~1.21.6", "java": ">=21", "fabric-api": "*"}, "suggests": {"another-mod": "*"}}